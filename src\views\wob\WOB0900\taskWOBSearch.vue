<template>
	<!--頁面內容 start-->
	<div class="container-fluid">
		<!--頁面內容 start-->
		<div class="card card-form-collapse" v-if="!isShowSummary">
			<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
				<h4>查詢條件</h4>
			</div>
			<div class="collapse show" id="collapseListGroup1">
				<div class="card-body">
					<vue-form v-slot="{errors}" ref="queryForm">

						<div class="row g-3 align-items-end">
							<div class="col-lg-6">
								<label class="form-label tx-require">事件產生日期</label>
								<div class="input-group">
									<div class="flex-grow-1">
										<vue-field type="date" name="startDate" id="startDate" class="form-control"
												   v-model="startDate" :class="{'is-invalid': errors.startDate}" label="事件產生日(起)"
												   rules="required" :min="minDt" :max="maxDt"></vue-field>
										<span class="text-danger" v-show="errors.startDate">{{errors.startDate}}</span>
									</div>
									<span class="input-group-text">~</span>
									<div class="flex-grow-1">
										<vue-field
												type="date" name="endDate" id="endDate" class="JQ-datepicker form-control"
												:class="{'is-invalid': errors.endDate}" label="事件產生日(迄)" rules="required"
												v-model="endDate" :min="minDt" :max="maxDt"></vue-field>
										<span class="text-danger" v-show="errors.endDate">{{errors.endDate}}</span>
									</div>
								</div>
							</div>
							<div class="col-lg-6">
								<label class="form-label">事件分類/類別</label>
								<div class="input-group">
									<select name="tdcat1Code" class="form-select" id="tdcat1Code" v-model="tdCat1Code">
										<option selected="selected" value="">請選擇</option>
										<option v-for="tdItemCat in tdItemCat1Menu" :value="tdItemCat.tdCat1Code">
											{{tdItemCat.tdCat1Name}}</option>
									</select>
									<select name="itemCode" class="form-select" id="wobTdItemsList" v-model="itemCode">
										<option value="">請選擇</option>
										<option v-for="tdItem in tdItemsMenu" :value="tdItem.itemCode">
											{{tdItem.itemName}}</option>
									</select>
								</div>
							</div>
							<div class="col-lg-6">
								<label class="form-label">客戶ID/統編 </label><br> <vue-field name="cusIdentity"
																							  class="form-control" id="cusIdentity" type="text" size="15" maxlength="20"
																							  :class="{'is-invalid': errors.cusIdentity}" label="顧客ID/統編" rules="min:8|max:20"
																							  v-model="cusCode"></vue-field>
								<div style="height: 3px">
									<span class="text-danger" v-show="errors.cusIdentity">請輸入完整顧客ID/統編</span>
								</div>
							</div>

							<div class="col-lg-6">
								<label class="form-label tx-require">組織 ( 區 / 分行 / AO)</label>
								<div class="input-group">
									<vue-field as="select" class="form-select" id="minorAreaBranCode" :class="{'is-invalid': errors.minorAreaBranCode}"
											   v-model="minorAreaBranCode" name="minorAreaBranCode" label="組織(區)" rules="required">
										<option value="">請選擇</option>
										<option v-for="minorAreaMenuData in minorAreaMenu" :value="minorAreaMenuData.branCode">{{minorAreaMenuData.branName}}</option>
									</vue-field>
									<select name="branCode" class="form-select" id="branCode" v-model="branCode">
										<option value="">全部</option>
										<option v-for="branMenuData in branMenu" :value="branMenuData.branCode">
											{{branMenuData.branCode}} {{branMenuData.branName}}</option>
									</select>
									<select name="rmUserCode" class="form-select" id="rmUserCode" v-model="rmUserCode">
										<option value="">全部</option>
										<option value="no">無經管</option>
										<option v-for="rmUser in branEmployeeMenu" :value="rmUser.userCode">
											{{rmUser.userCode}} {{rmUser.userName}}</option>
									</select>

								</div>
								<span class="text-danger" v-show="errors.minorAreaBranCode">{{errors.minorAreaBranCode}}</span>

							</div>
							<div class="form-footer col-12">
								<div class="tx-note float-start">
									篩選條件必選一個。
								</div>
								<button class="btn btn-primary btn-search" @click.prevent="gotoPage(0)">查詢</button>
							</div>

						</div>
					</vue-form>

				</div>
			</div>
		</div>
		<div class="searchResult">
			<div class="card card-table">
				<div class="card-header">
					<h4>查詢結果</h4>
					<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
				</div>
				<div class="table-responsive">
					<table class="table table-RWD table-bordered">
						<thead>
							<tr>
								<th>事件產生日</th>
								<th>分行</th>
								<th>經管人員</th>
								<th>客戶ID/統編<br />客戶姓名</th>
								<th width="10%">事件類別</th>
								<th width="10%">說明</th>
								<th>事件到期日</th>
								<th class="text-center">已結案</th>
								<th class="text-center">檢視</th>
							</tr>
						</thead>
						<tbody id="TopTenList">
							<tr v-for="item in pageData.content">
								<td data-th="事件產生日">{{$filters.formatDateTime(item.listsCreateDt)}}</td>
								<td data-th="分行">{{item.branCode}} {{item.branName}}</td>
								<td data-th="經管人員">{{item.regUserCode}} {{item.regUserName}}</td>
								<td data-th="客戶ID/統編	">{{item.cusCode}}<br> <a class="tx-link" href="#"
										@click="doViewSummary(item.cusCode)">{{item.cusName}}</a>
								</td>
								<td data-th="事件類別">{{item.itemName}}</td>
								<td data-th="說明">{{item.content}}</td>
								<td data-th="事件到期日">{{item.expireDt}}</td>
								<td class="text-center" data-th="狀態">
									<label v-if="item.doneYn == 'Y'">V</label>
								</td>
								<td class="text-center" data-th="檢視">
									<button type="button" class="btn btn-dark btn-icon" title="檢視" @click="doViewDetail(item)">
										<i class="bi bi-search"></i>
									</button>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
	<vue-cus-summary v-if="isShowSummary" ref="cusSummary" :set-is-show-summary="setIsShowSummary"></vue-cus-summary>
	<!-- Modal 1 start -->
	<vue-modal v-if="isOpenModal" :is-open="isOpenModal" :before-close="closeModal">
		<template v-slot:content="props">
		<div class="modal-dialog modal-lg modal-dialog-centered">
			<div class="modal-content">
				<div class="modal-header">
					<h4 class="modal-title">事件描述</h4>
					<button type="button" class="btn-expand"><i class="bi bi-arrows-fullscreen"></i></button>
					<button type="button" class="btn-close" @click.prevent="props.close()" aria-label="Close"></button>
				</div>
				<div class="modal-body" v-if="tdListDesc">
					<div class="card card-form mb-3">
						<div class="card-header">
							<h4>客戶ID/統編/姓名：{{tdListDesc.cusCode}}/{{tdListDesc.cusName}}</h4>
						</div>
						<table class="table table-RWD table-horizontal-RWD table-bordered">
							<tbody>
								<tr>
									<th>事件類別</th>
									<td colspan="3">{{tdListDesc.itemName}}</td>
								</tr>
								<tr>
									<th><label>說明：</label></th>
									<td colspan="3">{{tdListDesc.content}}</td>
								</tr>
								<tr>
									<th class="wd-15p"><label>事件產生日：</label></th>
									<td class="wd-35p">{{tdListDesc.listsCreateDt}}</td>
									<th class="wd-15p"><label>事件到期日：</label></th>
									<td class="wd-35p">2{{tdListDesc.expireDt}}</td>
								</tr>
								<tr>
									<th class="wd-15p"><label>送核日期：</label></th>
									<td class="wd-35p">{{tdListDesc.toVerifyDt}}</td>
									<th class="wd-15p"><label>最近維護日：</label></th>
									<td class="wd-35p">2{{tdListDesc.logsCreateDt}}</td>
								</tr>
								<tr>
									<th><label>覆核主管：</label></th>
									<td>{{tdListDesc.verifyUserName}}</td>
									<th><label>結案日期：</label></th>
									<td>{{tdListDesc.doneDt}}</td>
								</tr>
								<tr>
									<th><label>覆核日期：</label></th>
									<td>{{tdListDesc.modifyDt}}</td>
									<th><label>覆核狀態：</label></th>
									<td>{{tdListDesc.verifyStatusName}}</td>
								</tr>
								<tr>
									<th><label>處理人員：</label></th>
									<td>{{tdListDesc.createName}}</td>
									<th><label>處理方式：</label></th>
									<td>{{tdListDesc.actionName}}</td>
								</tr>
								<tr>
									<th><label>處理內容：</label></th>
									<td colspan="3">{{tdListDesc.memo}}</td>
								</tr>
								<tr>
									<th><label>處理狀態：</label></th>
									<td colspan="3">{{tdListDesc.statusName}}</td>
								</tr>
							</tbody>
						</table>
					</div>
					<div class="modal-footer" id="modalFooterId">
						<input class="btn btn-white" id="over1" type="button" value="關閉" @click.prevent="props.close()">
					</div>
				</div>
			</div>
		</div>
		<!-- Modal 1 End -->
		</template>
	</vue-modal>
	<!--頁面內容 end-->
</template>

<script>
import _ from 'lodash';
import Swal from 'sweetalert2';
import { Field, Form } from 'vee-validate';
import vueModal from '@/views/components/model.vue';
import vueCusSummary from '@/views/cus/include/cusSummary.vue';
import vuePagination from '@/views/components/pagination.vue';
import moment from 'moment';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueModal,
		vueCusSummary,
		vuePagination
	},
	data: function () {
		return {
			isOpenModal: null,
			startDate: null,
			endDate: null,
			cusCode: null,
			buCode: '',
			minorAreaBranCode: '',
			branCode: '',
			rmUserCode: '',
			tdCat1Code: '',
			itemCode: '',
			minDt: null,
			maxDt: null,
			//下拉選單
			minorAreaMenu: [],
			branMenu: [],
			branEmployeeMenu: [],
			tdItemCat1Menu: null,
			tdItemsMenu: null,
			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: 'LISTS_CREATE_DT ',
				direction: 'DESC'
			},
			tdListDesc: null,
			//畫面控制參數
			isShowSummary: false
		};
	},
	watch: {
		userInfo: {
			immediate: true,
			handler: function (newVal, oldVal) {
				var self = this;
				if (newVal) {
					self.buCode = newVal.buCode;
				}
			}
		},
		tdCat1Code: function () {
			var self = this;
			self.itemCode = '';
			self.getTdItemsMenu();
		},
		minorAreaBranCode: function (val) {
            console.log('minorAreaBranCode changed to:', val);
			var self = this;
			self.branCode = '';
			self.rmUserCode = '';
			self.branMenu = [];
			self.branEmployeeMenu = [];

			if (_.isBlank(val)) {
				return;
			}
			self.getBranMenu();
		},
		branCode: function (val) {
			var self = this;
			self.rmUserCode = '';
			self.branEmployeeMenu = [];
			if (_.isBlank(val)) {
				return;
			}
			self.getBranEmployeeMenu();
		}
	},
	computed: {
		userInfo: function () {
			return this.$store.getters['userInfo/info'];
		},
		isShowPageTitle: function () {
			return !this.isShowSummary;
		}
	},
	mounted: function () {
		var self = this;
		self.getMinorAreaMenu();
		self.getTdItemCat1Menu();
		self.maxDt = moment().format('YYYY-MM-DD');
		self.minDt = moment().subtract(2, 'years').format('YYYY-MM-DD'); // 兩年前的今天
	},
	methods: {
		getTdItemCat1Menu: function () {
			var self = this;
            self.$api.getTdItemCat1MenuApi().then(function(ret){self.tdItemCat1Menu = ret.data;})
			// var url = self.config.apiPath + '/adm/tdItemCat1Menu';
			// $.bi
			// 	.ajax({
			// 		url: url,
			// 		method: 'GET'
			// 	})
			// 	.then(function (ret) {
			// 		self.tdItemCat1Menu = ret.data;
			// 	});
		},
		getTdItemsMenu: function () {
            console.log('getTdItemsMenu called with tdCat1Code:', this.tdCat1Code);
			var self = this;
            self.$api.getTdItemsApi({ tdCat1Code: self.tdCat1Code }).then(function(ret){
				console.log('getTdItemsApi response:', ret);
				self.tdItemsMenu = ret.data || [];
				console.log('Updated tdItemsMenu:', self.tdItemsMenu);
			}).catch(function(error) {
				console.error('Error in getTdItemsApi:', error);
				self.tdItemsMenu = [];
			});
			// var url = self.config.apiPath + '/adm/tdItems';
			// $.bi
			// 	.ajax({
			// 		url: url,
			// 		method: 'GET',
			// 		data: {
			// 			tdCat1Code: self.tdCat1Code
			// 		}
			// 	})
			// 	.then(function (ret) {
			// 		self.tdItemsMenu = ret.data;
			// 	});
		},
		// 取得組織(區)選單
		getMinorAreaMenu: function () {
			var self = this;
			// var minorAreaMenuUrl = self.config.apiPath + '/adm/minorArea';
			console.log(self.buCode,self.majorCode,'getMinorAreaMenu buCode,majorCode')
            self.$api.getMinorAreaApi({ buCode:self.buCode, majorCode:self.majorCode }).then(function (ret) {
				self.minorAreaMenu = ret.data;
					if (self.minorAreaMenu.length == 1) {
						self.minorAreaBranCode = self.minorAreaMenu[0].branCode;
                        
					}
			});

			// $.bi
			// 	.ajax({
			// 		url: minorAreaMenuUrl,
			// 		method: 'GET'
			// 	})
			// 	.then(function (ret) {
			// 		self.minorAreaMenu = ret.data;
			// 		if (self.minorAreaMenu.length == 1) {
			// 			self.minorAreaBranCode = self.minorAreaMenu[0].branCode;
			// 		}
			// 	});
		},
		// 取得組織(分行)選單
        getBranMenu: function () {
        var self = this;
        console.log('取得組織(分行)選單')
        self.$api.getBranchesApi({ buCode:self.buCode, majorCode:self.majorCode, minorCode:self.minorCode }).then(function (ret) {
        self.branMenu = ret.data;
		console.log('ret',ret)
        if (self.branMenu.length === 1) {
            self.branCode = self.branMenu[0].branCode;
        }
        })},
		// 取得組織(AO)選單
		getBranEmployeeMenu: function () {
			var self = this;
			// var url = self.config.apiPath + '/adm/branEmployee';

			var branCode = null;
            
			if (!_.isBlank(self.minorAreaBranCode)) {
				branCode = self.minorAreaBranCode;
			}

			if (!_.isBlank(self.branCode)) {
				branCode = self.branCode;
			}
            console.log('branCode',branCode)
            self.$api.getBranEmployeeApi({buCode:self.buCode,branCode:branCode}).then(function (ret) {
                console.log('api callback')
                self.branEmployeeMenu = ret.data;
				if (self.branEmployeeMenu.length == 1) {
					self.rmUserCode = self.branEmployeeMenu[0].userCode;
				}
            });
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: function (page) {
			var self = this;
			var url =  '/wob/serachTdList';

			var page = _.isNumber(page) ? page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;
			console.log(self.pageable,'selfpageable')
			console.log('url',url)
			self.$refs.queryForm.validate().then(function (pass) {
				if (moment(self.endDate).diff(moment(self.startDate), 'years', true) > 1) {
					Swal.fire({
						icon: 'error',
						text: '查詢日期區間不能大於一年',
						showCloseButton: true,
						confirmButtonText: '確認',
						buttonsStyling: false,
						customClass: {
							confirmButton: 'btn btn-danger'
						}
					});
					return;
				}
				let data = {};
				data.startDate = _.formatDate(self.startDate);
				data.endDate = _.formatDate(self.endDate);
				data.tdCat1Code = self.tdCat1Code;
				data.itemCode = self.itemCode;
				data.cusCode = self.cusCode;
				data.pageable = self.pageable;

				if (_.isBlank(self.branCode)) {
					data.allBranCode = self.branMenu.map((item) => item.branCode);
				} else {
					data.branCode = self.branCode;
				}
				if (_.isBlank(self.rmUserCode)) {
					data.allUserCode = self.branEmployeeMenu.map((item) => item.userCode);
				} else {
					data.userCode = self.rmUserCode;
				}

				if (pass.valid) {
					let {startDate,endDate,tdCat1Code,itemCode,cusCode,branCode,allBranCode,userCode,allUserCode,pageable} = data;
					console.log('data',startDate,endDate,tdCat1Code,itemCode,cusCode,branCode,
					allBranCode,userCode,allUserCode);
					self.$api.getWobSerachTdListApi({startDate,endDate,tdCat1Code,itemCode,cusCode,branCode,
					allBranCode,userCode,allUserCode,pageable}).then(function (ret) {
						self.pageData = ret.data;
					})
					// $.bi
					// 	.ajax({
					// 		url: url,
					// 		method: 'GET',
					// 		data: data
					// 	})
					// 	.then(function (ret) {
					// 		self.pageData = ret.data;
					// 	});
				}
			});
		},
		doViewDetail: function (item) {
			var self = this;
			// var url = self.config.apiPath + '/wob/tdListDesc';
			console.log('item',item)
			self.$api.getWobTdListDescApi({ tdKind: item.tdKind, tdCode: item.tdCode }).then(function (ret) {
				console.log('retdata',ret)
				
				self.tdListDesc = ret.data;
				self.isOpenModal = true;
				console.log('self.tdListDesc',self.tdListDesc,self.isOpenModal)
			})
			// $.bi
			// 	.ajax({
			// 		url: url,
			// 		method: 'GET',
			// 		data: {
			// 			tdKind: item.tdKind,
			// 			tdCode: item.tdCode
			// 		}
			// 	})
			// 	.then(function (ret) {
			// 		self.tdListDesc = ret.data;
			// 		self.isOpenModal = true;
			// 	});
		},

		setIsShowSummary: function (val) {
			var self = this;
			self.isShowSummary = val;
		},
		doViewSummary: function (cusCode) {
			var self = this;
			self.isShowSummary = true;
			self.connectCusCode = cusCode;
			this.$refs.cusSummary.setCusCode(cusCode);
		},
		closeModal: function () {
			this.isOpenModal = false;
		}
	}
    
};
</script>
