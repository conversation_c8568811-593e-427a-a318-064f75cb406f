import request from '@/utils/request';
const apiPath = import.meta.env.VITE_API_URL_V1;

export function postPersonalTaskApi({ nextRemindDt, nextRemindTime, advNce, advNceDay, advNcePrd, title, content }) {
	return request({
		url: apiPath + '/wob/tdPersonalRec',
		method: 'post',
		data: {
			nextRemindDt,
			nextRemindTime,
			advNce,
			advNceDay,
			advNcePrd,
			title,
			content
		}
	});
}

export function postVisit({ cusCode, nextRemindDt, nextRemindTime, advNce, advNceDay, advNcePrd, visitAprCode, visitPurCode, title, content }) {
	return request({
		url: apiPath + '/wob/tdRec',
		method: 'post',
		data: {
			cusCode,
			nextRemindDt,
			nextRemindTime,
			advNce,
			advNceDay,
			advNcePrd,
			visitAprCode,
			visitPurCode,
			title,
			content
		}
	});
}

export function postConnect({ cusCode, nextRemindDt, nextRemindTime, title, visitAprCode, content, contStatCode, contProcCode, doneYn }) {
	return request({
		url: apiPath + '/wob/tdConnRec',
		method: 'post',
		data: {
			cusCode,
			nextRemindDt,
			nextRemindTime,
			title,
			visitAprCode,
			content,
			contStatCode,
			contProcCode,
			doneYn
		}
	});
}

export function postMemoryDateApi({ cusCode, dateDt, note, remindYn, remindDays, remindPrd }) {
	return request({
		url: apiPath + '/wob/memoryDate',
		method: 'post',
		data: {
			cusCode,
			dateDt,
			note,
			remindYn,
			remindDays,
			remindPrd
		}
	});
}

export function getReuseWordsApi() {
	return request({
		url: apiPath + '/wob/reuseWords',
		method: 'get'
	});
}

export function postReuseWordsApi({ wordsId, words }) {
	return request({
		url: apiPath + '/wob/reuseWords',
		method: 'post',
		data: {
			wordsId,
			words
		}
	});
}

export function updateReuseWordsApi(dataArray) {
	return request({
		url: apiPath + '/wob/reuseWords',
		method: 'patch',
		data: [...dataArray]
	});
}

export function getCalendarTasksApi({ startDate, endDate }) {
	return request({
		url: apiPath + '/wob/calendarTasks',
		method: 'get',
		params: {
			startDate,
			endDate
		}
	});
}

export function getTdRecApi({ recCode }) {
	return request({
		url: apiPath + '/wob/tdRec',
		method: 'get',
		params: {
			recCode
		}
	});
}

export function deleteTdRecApi({ recCode }) {
	return request({
		url: apiPath + '/wob/tdRec',
		method: 'delete',
		params: {
			recCode
		}
	});
}

export function patchTdPersonalRecApi({ tdRec, nextRemindDt, nextRemindTime, title, content, advNce, advNceDay, advNcePrd }) {
	return request({
		url: apiPath + '/wob/tdPersonalRec',
		method: 'patch',
		data: {
			tdRec,
			nextRemindDt,
			nextRemindTime,
			title,
			content,
			advNce,
			advNceDay,
			advNcePrd
		}
	});
}

export function getAppointmentTdRecApi({ recCode }) {
	return request({
		url: apiPath + '/wob/appointmentTdRec',
		method: 'get',
		params: {
			recCode
		}
	});
}

export function patchAppointmentTdRecApi({
	recCode,
	cusCode,
	nextRemindDt,
	nextRemindTime,
	visitPurCode,
	visitAprCode,
	title,
	advNce,
	advNceDay,
	advNcePrd,
	content,
	doneYn,
	verifyStatusCode
}) {
	return request({
		url: apiPath + '/wob/appointmentTdRec',
		method: 'patch',
		data: {
			recCode,
			cusCode,
			nextRemindDt,
			nextRemindTime,
			visitPurCode,
			visitAprCode,
			title,
			advNce,
			advNceDay,
			advNcePrd,
			content,
			doneYn,
			verifyStatusCode
		}
	});
}

export function getMemoryCalendarTaskApi({ id }) {
	return request({
		url: apiPath + '/wob/memoryCalendarTask',
		method: 'get',
		params: {
			id
		}
	});
}

export function deleteMemoryDateApi({ id }) {
	return request({
		url: apiPath + '/wob/memoryDate',
		method: 'delete',
		params: {
			id
		}
	});
}

export function patchMemoryDateApi({ id, cusCode, dateDt, note, remindDays, remindPrd, remindYn }) {
	return request({
		url: apiPath + '/wob/memoryDate',
		method: 'patch',
		params: {
			id,
			cusCode,
			dateDt,
			note,
			remindDays,
			remindPrd,
			remindYn
		}
	});
}

export function getTdConnRecApi({ recCode }) {
	return request({
		url: apiPath + '/wob/tdConnRec',
		method: 'get',
		params: {
			recCode
		}
	});
}

export function patchTdConnRecApi({
	recCode,
	cusCode,
	nextRemindDt,
	nextRemindTime,
	doneYn,
	title,
	content,
	visitAprCode,
	contStatCode,
	contProcCode,
	contProcDesc,
	visitUserName,
	verifyStatusCode,
	giftId,
	giftDesc
}) {
	return request({
		url: apiPath + '/wob/tdConnRec',
		method: 'patch',
		data: {
			recCode,
			cusCode,
			nextRemindDt,
			nextRemindTime,
			doneYn,
			title,
			content,
			visitAprCode,
			contStatCode,
			contProcCode,
			contProcDesc,
			visitUserName,
			verifyStatusCode,
			giftId,
			giftDesc
		}
	});
}

export function postWobReuseWordsApi({ wordsId, words }) {
	return request({
		url: apiPath + '/wob/wobReuseWords',
		method: 'post',
		data: {
			wordsId,
			words
		}
	});
}

export function getWobReuseWordsApi() {
	return request({
		url: apiPath + '/wob/wobReuseWords',
		method: 'get'
	});
}
export function getWobSerachTdListApi({ startDate, endDate, tdCat1Code, itemCode, cusCode, branCode, allBranCode, userCode, allUserCode, pageable }) {
	return request({
		url: apiPath + '/wob/serachTdList',
		method: 'get',
		params: {
			startDate,
			endDate,
			tdCat1Code,
			itemCode,
			cusCode,
			branCode,
			allBranCode,
			userCode,
			allUserCode,
			pageable
		}
	});
}
